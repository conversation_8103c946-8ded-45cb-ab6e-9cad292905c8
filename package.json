{"name": "shopwith99", "version": "1.0.0", "description": "online store", "main": "server.js", "scripts": {"start": "node server.js"}, "author": "Danilo99", "license": "ISC", "dependencies": {"bcrypt": "^5.1.1", "body-parser": "^1.20.2", "cloudinary": "^2.0.1", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.18.2", "express-formidable": "^1.2.0", "flutterwave-node-v3": "^1.1.9", "jsonwebtoken": "^9.0.2", "mongoose": "^8.1.3", "multer": "^1.4.5-lts.1", "nodemon": "^3.0.3", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "validator": "^13.11.0"}, "devDependencies": {"@types/multer": "^1.4.11"}}